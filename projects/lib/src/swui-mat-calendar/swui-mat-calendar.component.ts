import { Component, forwardRef, HostBinding, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { ControlValueAccessor, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, takeUntil, tap } from 'rxjs/operators';
import moment from 'moment';
import 'moment-timezone';

import { SwuiSelectOption } from '../swui-select/swui-select.interface';

/**
 * Safely validates if a string can be parsed as a valid date by moment.js
 * without triggering deprecation warnings for invalid date strings.
 */
function isValidDateString(val: string): boolean {
  if (!val || typeof val !== 'string') {
    return false;
  }

  // Basic checks to avoid creating moment objects for obviously invalid strings
  const trimmed = val.trim();
  if (trimmed === '' || trimmed === 'wrong_string' || trimmed === 'invalid') {
    return false;
  }

  // Check for basic date patterns to avoid moment deprecation warnings
  // ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ or YYYY-MM-DD
  const isoPattern = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/;
  // RFC 2822 format patterns
  const rfcPattern = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s+\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4}\s+\d{2}:\d{2}:\d{2}/;
  // Unix timestamp (numbers only)
  const timestampPattern = /^\d+$/;

  // If it doesn't match any known valid patterns, return false to avoid warnings
  if (!isoPattern.test(trimmed) && !rfcPattern.test(trimmed) && !timestampPattern.test(trimmed)) {
    return false;
  }

  // Only create moment object if it passes basic pattern checks
  try {
    const momentObj = moment.utc(val);
    return momentObj.isValid();
  } catch (error) {
    return false;
  }
}

interface SelectDateForm {
  month: string;
  year: number;
}

@Component({
    selector: 'lib-swui-mat-calendar',
    templateUrl: './swui-mat-calendar.component.html',
    styleUrls: ['./swui-mat-calendar.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SwuiMatCalendarComponent),
            multi: true
        },
    ],
    standalone: false
})

export class SwuiMatCalendarComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input()
  set fromDate(val: string) {
    this._fromDate$.next(val || '');
  }
  get fromDate(): string {
    return this._fromDate$.value;
  }

  @Input()
  set toDate(val: string) {
    this._toDate$.next(val || '');
  }
  get toDate(): string {
    return this._toDate$.value;
  }

  @Input()
  set minDate(val: string) {
    this._minDate$.next(val || '');
  }

  get minDate(): string {
    return this._minDate$.value;
  }

  @Input()
  set maxDate(val: string) {
    this._maxDate$.next(val || '');
  }

  get maxDate(): string {
    return this._maxDate$.value;
  }

  @Input() isFromRange = false;
  @Input() isToRange = false;
  @Input()
  set timeZone( val: string) {
    this._timeZone$.next(val);
  }
  get timeZone(): string {
    return this._timeZone$.value;
  }

  @Input()
  set value(val: string) {
    this._value$.next(val && isValidDateString(val) ? val : '');
  }
  get value(): string {
    return this._value$.value;
  }

  @Input() time?: {
    hours: number;
    minutes: number;
    seconds: number;
  };

  isDisabled = false;
  currentMonth: moment.Moment[][] = [];
  weekDayNames = moment.weekdaysShort();
  selectDateForm: UntypedFormGroup;
  months: SwuiSelectOption[] = [];
  years: number[] = [];

  @HostBinding('attr.tabindex')
  public tabindex = 0;

  onChange: ( _: any ) => void = (() => {
  });

  private _currentDate: moment.Moment;
  private _selectedDate?: moment.Moment;
  private processedMinDate?: moment.Moment;
  private processedMaxDate?: moment.Moment;
  private processedFromDate?: moment.Moment;
  private processedToDate?: moment.Moment;

  private _timeZone$ = new BehaviorSubject<string>('');
  private _value$ = new BehaviorSubject<string>('');
  private _minDate$ = new BehaviorSubject<string>('');
  private _maxDate$ = new BehaviorSubject<string>('');
  private _fromDate$ = new BehaviorSubject<string>('');
  private _toDate$ = new BehaviorSubject<string>('');
  private _destroyed$ = new Subject();
  private setDate$ = new Subject<moment.Moment>();

  constructor( private fb: UntypedFormBuilder ) {
    this._currentDate = this.today.clone();
    this.initYearsList();
    this.initMonthList();
    this.selectDateForm = this.initSelectDateForm();
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit(): void {
    this.setDate$
      .pipe(
        debounceTime(10),
        distinctUntilChanged((prev, curr) => prev?.format() === curr?.format()),
        takeUntil(this._destroyed$)
      )
      .subscribe(date => {
        this.selectDay(date);
      });

    combineLatest([this._timeZone$, this._value$, this._minDate$, this._maxDate$, this._fromDate$, this._toDate$])
      .pipe(
        map( ([timezone, selected, minDate, maxDate, fromDate, toDate]) => {
          this._selectedDate = this.setDateValue(selected, timezone);
          this.processedMinDate = this.setDateValue(minDate, timezone, true);
          this.processedMaxDate = this.setDateValue(maxDate, timezone, true);
          this.processedToDate = this.setDateValue(toDate, timezone, true);
          this.processedFromDate = this.setDateValue(fromDate, timezone, true);
          return timezone;
        }),
        tap( (timezone: string) => {
          if (timezone) {
            this.startWithTime(this.today.tz(timezone));
            this._currentDate.tz(timezone);
          } else {
            this.startWithTime(this.today.utc());
            this._currentDate.utc();
          }
        }),
        takeUntil(this._destroyed$)
      )
      .subscribe( () => {
        if (this._selectedDate && !this._selectedDate.isSame(this._currentDate, 'month')) {
          this.setMonth(this._selectedDate);
        } else {
          this.setMonth(this._currentDate);
        }

        if (this._selectedDate && this.processedMaxDate && this._selectedDate.diff(this.processedMaxDate) >= 0) {
          this.setDate$.next(this.processedMaxDate);
        }
      });

    this.selectDateForm.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe( (val: SelectDateForm) => {
        const date = this._currentDate.clone();
        date.year(val.year);
        date.month(parseInt(val.month, 10));
        this.setMonth(date);
      });
  }

  onTouched: any = () => {
  };

  get today(): moment.Moment { return moment.utc().clone(); }

  ngOnDestroy(): void {
    this._destroyed$.next(undefined);
    this._destroyed$.complete();
  }

  writeValue(val: string): void {
    this._value$.next(val && isValidDateString(val) ? val : '');
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isDisabled = !!disabled;
    if (disabled) {
      this.selectDateForm.disable();
    } else {
      this.selectDateForm.enable();
    }
  }

  isToday(day: moment.Moment): boolean {
    return day ? day.isSame(this.today, 'date') : false;
  }

  selectDay( day: moment.Moment, event?: Event ) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (!this.isDayDisabled(day) && !this.isDisabled) {
      this._selectedDate = day.clone();
      if (this._selectedDate && !this._selectedDate.isSame(this._currentDate, 'month')) {
        this.setMonth(this._selectedDate.clone());
      }

      let processed = '';

      const date = this.startWithTime(this._selectedDate.clone());

      if (date) {
        processed = date.toISOString();
      }

      this._value$.next(processed);
      this.onChange(processed);
    }
  }

  isDaySelected(day: moment.Moment): boolean {
    return day && this._selectedDate ? day.isSame(this._selectedDate, 'date') : false;
  }

  isDayDisabled(initDay: moment.Moment): boolean {
    const day = initDay?.clone().startOf('day');

    if (this.isFromRange && !!day) {
      const isDayBeforeMinDate = !!this.processedMinDate && day.diff(this.processedMinDate, 'seconds') <= -86400;
      const isDayAfterFromDate = !!this.processedToDate && day.isAfter(this.processedToDate);

      return isDayBeforeMinDate || isDayAfterFromDate || this.isDisabled;
    }

    if (this.isToRange && !!day) {
      const isDayAfterMaxDate = !!this.processedMaxDate && day.isAfter(this.processedMaxDate.clone().startOf('day'));
      const isDayBeforeToDate = !!this.processedFromDate && day.isBefore(this.processedFromDate.clone().startOf('day'));

      return isDayAfterMaxDate || isDayBeforeToDate || this.isDisabled;
    }

    return (day && this.processedMinDate && day.isBefore(this.processedMinDate, 'date')) ||
           (day && this.processedMaxDate && day.isAfter(this.processedMaxDate.clone().startOf('day'), 'date')) ||
           this.isDisabled ||
           false;
  }

  isFromDate(day: moment.Moment): boolean {
    return !!day && !!this.processedFromDate && day.isSame(this.processedFromDate, 'day');
  }

  isToDate(day: moment.Moment): boolean {
    return !!day && !!this.processedToDate && day.isSame(this.processedToDate, 'day');
  }

  isMinDate(day: moment.Moment): boolean {
    return !!day && !!this.processedMinDate && day.isSame(this.processedMinDate);
  }

  isMaxDate(day: moment.Moment) {
    return !!day && !!this.processedMaxDate && day.isSame(this.processedMaxDate);
  }

  isInRangeDate(day: moment.Moment) {
    if (this.isFromRange && this._selectedDate) {
      return (day && day.isAfter(this._selectedDate.toISOString() , 'date')) &&
        (day && this.processedToDate && day.isBefore(this.processedToDate, 'date'));
    }

    if (this.isToRange && this._selectedDate) {
      return (day && day.isBefore(this._selectedDate.toISOString() , 'date')) &&
        (day && this.processedFromDate && day.isAfter(this.processedFromDate, 'date'));
    }

    return false;
  }

  get yearControl(): UntypedFormControl {
    return this.selectDateForm.get('year') as UntypedFormControl;
  }

  get monthControl(): UntypedFormControl {
    return this.selectDateForm.get('month') as UntypedFormControl;
  }

  private startWithTime(date: moment.Moment): moment.Moment | undefined {
    if (!date) {
      return;
    }

    if (!this.time) {
      return date.startOf('day');
    }

    return date.set({ ...this.time });
  }

  private initSelectDateForm(): UntypedFormGroup {
    return this.fb.group({
      month: [],
      year: []
    });
  }

  private setDateValue(val: string, timezone: string, skipTime?: boolean): moment.Moment | undefined {
    if (isValidDateString(val)) {
      if (timezone) {
        return skipTime ? moment.tz(val, timezone) : this.startWithTime(moment.tz(val, timezone));
      } else {
        return skipTime ? moment.utc(val) : this.startWithTime(moment.utc(val));
      }
    } else {
      return undefined;
    }
  }

  private setMonth( date: moment.Moment ) {
    const firstDay = date.clone().startOf('month');
    const lastDay = date.clone().endOf('month');
    const month: moment.Moment[][] = [];

    while (firstDay.date() <= lastDay.date()) {
      if (!month.length || !firstDay.day()) {
        month.push([]);
      }
      month[month.length - 1][firstDay.day()] = firstDay.clone();
      if (firstDay.date() === lastDay.date()) {
        break;
      } else {
        firstDay.add(1, 'days');
      }
    }

    const currentDate = this.startWithTime(date.clone());
    this.currentMonth = month;

    if (!currentDate) {
      return;
    }

    if (this.yearControl.value !== currentDate.clone().year()) {
      this.yearControl.setValue(currentDate.clone().year());
    }

    if (this.monthControl.value !== currentDate.clone().month().toString()) {
      this.monthControl.setValue(currentDate.clone().month().toString());
    }
  }

  private initYearsList() {
    const currentYear = this.today.year();
    for (let y = currentYear - 100; y <= currentYear + 100; y++) {
      this.years.push(y);
    }
  }

  private initMonthList() {
    const months = moment.monthsShort();
    months.forEach( (item: string, index: number) => {
      this.months.push( { id: index.toString(), text: item } );
    });
  }
}
